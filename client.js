const fs = require('fs');
const WebSocket = require('ws');

// API Key’in (gizli tut!)
const OPENAI_API_KEY = '********************************************************************************************************************************************************************'; // gerçek API key ile değiştir

// OpenAI Realtime socket endpoint
const ws = new WebSocket('wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2025-06-03', {
  headers: {
    Authorization: `Bearer ${OPENAI_API_KEY}`,
    'OpenAI-Beta': 'realtime=v1',

  }
});

// 8kHz PCM dosyayı oku (sadece ham PCM16LE, WAV değil)
const audioStream = fs.createReadStream('./uploads/baskent8.pcm');

ws.on('open', () => {
  console.log('[WS] Bağlandı');

  // Başlangıç mesajı
  ws.send(JSON.stringify({
    type: 'session.update',
    session: {
      modalities               : ['text', 'audio'],
      turn_detection           : { type: 'server_vad' },
      voice                    : 'sage',
      input_audio_transcription: {
        model   : 'whisper-1',
        language: "tr"
      },
      input_audio_format       : 'pcm16',
      output_audio_format      : 'pcm16',
    }
  }));

  // Veriyi parça parça gönder
  const chunkSize = 320; // 20ms için 320 byte (8000Hz * 2byte * 0.02s)
  const intervalMs = 20;

  const sendAudio = () => {
    const chunk = audioStream.read(chunkSize);
    if (chunk) {
      ws.send(chunk);
      setTimeout(sendAudio, intervalMs);
    } else {
      ws.send(JSON.stringify({ type: 'stop' }));
      console.log('[WS] Tüm ses gönderildi.');
    }
  };

  audioStream.on('readable', () => {
    sendAudio();
  });
});

ws.on('message', (data) => {
  const msg = JSON.parse(data);
  if (msg.text) {
    console.log('✏️ Transkript:', msg.text);
  } else {
    console.log('[WS] Mesaj:', msg);
  }
});

ws.on('close', () => {
  console.log('[WS] Bağlantı kapandı');
});

ws.on('error', (err) => {
  console.error('[WS] Hata:', err);
});
