import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { BaseServiceInterface } from '../interfaces/base-service.interface';
import { BaseEntity } from './base.entity';
import { BaseDto } from './base.dto';

@Injectable()
export class BaseService<T extends BaseEntity<T>, D extends BaseDto>
  implements BaseServiceInterface<T, D>
{
  constructor(protected readonly repository: T | any) {}

  async getAll() {
    return this.repository.findAll() as T[];
  }

  async get(id: string) {
    const model = (await this.repository.findByPk(id)) as T;
    if (!model) {
      throw new HttpException(
        'Model with given id not found',
        HttpStatus.NOT_FOUND
      );
    }
    return model;
  }

  async create(model: Partial<D>) {
    return this.repository.create({ ...model });
  }

  async update(id: string, model: D) {
    return await this.get(id).then((responseGet) => {
      responseGet.setAttributes(model as any);
      responseGet.save();
      return responseGet as T;
    });
  }

  async delete(id: string) {
    const user = await this.repository.findByPk(id);
    return user.destroy();
  }
}
