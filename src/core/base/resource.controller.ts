import { Body, Delete, Get, Param, Post, Put } from '@nestjs/common';
import { ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { BaseEntity } from './base.entity';
import { BaseDto } from './base.dto';
import { BaseService } from './base.service';
import { ResourceEntity } from './resource.entity';

export class ResourceController<
  T extends ResourceEntity<T>,
  D extends BaseDto,
> {
  constructor(private readonly service: BaseService<T, D>) {}

  @Get()
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: 'Ok' })
  async findAll(): Promise<T[]> {
    return this.service.getAll();
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: 'Resource retrieved successfully.' })
  @ApiResponse({ status: 404, description: 'Resource does not exist' })
  async findById(@Param('id') id: string): Promise<T> {
    return this.service.get(id);
  }

  @Post()
  @ApiBearerAuth()
  @ApiResponse({ status: 201, description: 'The record created successfully.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async create(@Body() model: D): Promise<T> {
    return this.service.create(model);
  }

  @Put()
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: 'Resource updated successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async update(@Param('id') id: string, @Body() model: D): Promise<T> {
    return this.service.update(id, model);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: 'Resource deleted successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async delete(@Param('id') id: string) {
    this.service.delete(id);
  }
}
