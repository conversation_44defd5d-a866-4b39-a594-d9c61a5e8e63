import { Sequelize } from 'sequelize-typescript';
import { User } from '../../modules/users/user.entity';
import { ConfigService } from '@nestjs/config';
import { Asset } from '../../modules/asset/asset.entity';

export const databaseProviders = [
  {
    provide: 'SEQUELIZE',
    useFactory: async (configService: ConfigService) => {
      const sequelize = new Sequelize(configService.get('database'));
      sequelize.addModels([User, Asset]);
      await sequelize.sync();
      return sequelize;
    },
    inject: [ConfigService],
  },
];
