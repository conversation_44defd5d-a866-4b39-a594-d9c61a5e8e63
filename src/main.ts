import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { setupSwagger } from './core/swagger';
import { ValidationError, ValidationPipe } from '@nestjs/common';
import { UniqueConstraintErrorFilter } from './core/filters/htttp-exception.filter';
import { ValidationExceptionFilter } from './core/filters/validation-exception.filter';
import { ValidationException } from './core/exceptions/validation.exception';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(
    new ValidationPipe({
      exceptionFactory: (validationErrors: ValidationError[] = []) => {
        return new ValidationException(validationErrors);
      },
    })
  );
  app.useGlobalFilters(new UniqueConstraintErrorFilter());
  app.useGlobalFilters(new ValidationExceptionFilter());
  setupSwagger(app);
  await app.listen(process.env.PORT ?? 3000);
}

bootstrap();
