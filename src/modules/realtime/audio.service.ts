import { Injectable } from '@nestjs/common';
import { Readable } from 'stream';
import * as ffmpeg from 'fluent-ffmpeg';
import { WavService } from './wav.service';

@Injectable()
export class AudioService {
  constructor(
    private readonly wavService: WavService
  ) {}

  // Converts Float32Array of audio data to PCM16 ArrayBuffer
  floatTo16BitPCM(float32Array) {
    const buffer = new ArrayBuffer(float32Array.length * 2);
    const view = new DataView(buffer);
    let offset = 0;
    for (let i = 0; i < float32Array.length; i++, offset += 2) {
      let s = Math.max(-1, Math.min(1, float32Array[i]));
      view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
    }
    return buffer;
  }

  // Converts a Float32Array to base64-encoded PCM16 data
  base64EncodeAudio(float32Array) {
    const arrayBuffer = this.floatTo16BitPCM(float32Array);
    let binary = '';
    let bytes = new Uint8Array(arrayBuffer);
    const chunkSize = 0x8000; // 32KB chunk size
    for (let i = 0; i < bytes.length; i += chunkSize) {
      let chunk = bytes.subarray(i, i + chunkSize);
      binary += String.fromCharCode.apply(null, chunk);
    }
    return btoa(binary);
  }

  async convertWavToPCM16(audioFile) {
    return  this.wavService.convertWavToPcm(audioFile);
  }

  async convertTo16Khz(audioFile, outputFile) {
    return this.wavService.convertPCM8kTo16k(audioFile,outputFile);
  }


  createWavFileFromPCM(
    pcmBuffer,
    sampleRate = 24000,
    numChannels = 1,
    bitsPerSample = 16
  ) {
    const byteRate = (sampleRate * numChannels * bitsPerSample) / 8;
    const blockAlign = (numChannels * bitsPerSample) / 8;
    const dataSize = pcmBuffer.length;

    const header = Buffer.alloc(44);
    header.write('RIFF', 0); // ChunkID
    header.writeUInt32LE(36 + dataSize, 4); // ChunkSize
    header.write('WAVE', 8); // Format
    header.write('fmt ', 12); // Subchunk1ID
    header.writeUInt32LE(16, 16); // Subchunk1Size
    header.writeUInt16LE(1, 20); // AudioFormat (1 = PCM)
    header.writeUInt16LE(numChannels, 22); // NumChannels
    header.writeUInt32LE(sampleRate, 24); // SampleRate
    header.writeUInt32LE(byteRate, 28); // ByteRate
    header.writeUInt16LE(blockAlign, 32); // BlockAlign
    header.writeUInt16LE(bitsPerSample, 34); // BitsPerSample
    header.write('data', 36); // Subchunk2ID
    header.writeUInt32LE(dataSize, 40); // Subchunk2Size

    return Buffer.concat([header, pcmBuffer]);
  }

  convertPcmToWavBinary8Khz(inputFile, outputFile): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg()
        // .input(inputStream)
        .input(inputFile)
        .inputFormat('s16le') // PCM16LE formatı
        .inputOptions([
          '-ar 24000', // Giriş sample rate: 24kHz
          '-ac 1', // Mono kanal
        ])
        .audioCodec('pcm_s16le') // WAV için PCM codec
        .audioFrequency(8000) // Çıkış sample rate: 8kHz
        .audioChannels(1) // Mono kanal çıkış
        .format('wav') // WAV formatı
        .output(outputFile)
        .on('start', (commandLine) => {
          console.log('FFmpeg komutu:', commandLine);
          console.log('Buffer to WAV dönüştürme başladı...');
        })
        .on('end', () => {
          console.log('FFmpeg işlemi tamamlandı');
          resolve(outputFile);
        })
        .on('error', (err) => {
          console.error('FFmpeg hatası:', err.message);
          reject(err);
        })
        .run();

    });
  }

  bufferToStream(buffer) {
    const stream = new Readable();
    stream.push(buffer);
    stream.push(null); // EOF
    return stream;
  }
}
