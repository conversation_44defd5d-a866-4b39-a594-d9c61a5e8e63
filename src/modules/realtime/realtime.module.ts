import { Module } from '@nestjs/common';
import { RealtimeController } from './realtimeController';
import { RealtimeService } from './realtime.service';
import { OpenAiRealtimeService } from './openai-realtime.service';
import { AudioService } from './audio.service';
import { WavService } from './wav.service';

@Module({
  controllers: [RealtimeController],
  providers: [RealtimeService, OpenAiRealtimeService, AudioService, WavService],
  exports: [RealtimeService, AudioService, WavService],
})
export class RealtimeModule {}
