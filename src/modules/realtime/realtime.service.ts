import { Injectable } from '@nestjs/common';
import { OpenAiRealtimeService } from './openai-realtime.service';
import { ResponseData } from './types';
import * as fs from 'fs';
import { AudioService } from './audio.service';

@Injectable()
export class RealtimeService {
  public constructor(
    private readonly openAiRealtimeService: OpenAiRealtimeService,
    private readonly audioService: AudioService
  ) {}

  public async start() {
    return this.openAiRealtimeService.connect();
  }

  public async sendChunk(audio) {
    this.openAiRealtimeService.sendToModel({
      type: 'input_audio_buffer.append',
      audio: audio,
    });
  }

  public async responseCreate(onDone = (data) => {}) {
    this.openAiRealtimeService.sendToModel({
      type: 'response.create',
    });

    let response = '';

    this.openAiRealtimeService.responseCallback((data: ResponseData) => {
      if (data.event === 'delta') {
        response += data.media?.payload;
      }

      if (data.event === 'done') {
        // console.log('Done', response);
        this.modelSoundDone(response, onDone);
      }
    });
  }

  async sendWaveAudio(file, onDone = (data) => {}) {
    // const file16k = 'uploads/output16.wav';
    // await this.audioService.convertTo16Khz(file, file16k);
    // const rawWav = fs.readFileSync(file16k);
    // const audioBuffer = await this.audioService.convertWavToPCM16(rawWav);
    // fs.writeFile('uploads/input.pcm', audioBuffer, () => {
    //   console.log('file saved', 'uploads/input.pcm');
    // });
    //
    // this.sendChunk(audioBuffer.toString('base64'));
    this.sendChunk(fs.readFileSync(file).toString('base64'));
    await new Promise((resolve) => setTimeout(resolve, 1000));
    this.responseCreate(onDone);
  }

  private async modelSoundDone(base64String: string, onDone = (data) => {}) {
    const buffer = Buffer.from(base64String, 'base64');
    fs.writeFile('uploads/model-response.bin', buffer, () => {
      console.log("PCM dosyası oluşturuldu: model-response.bin'");
    });

    // const wavBuffer = await this.audioService.convertPcmToWavBinary8Khz(buffer);

    const inputFile = 'uploads/model-response.bin';
    const outputFile = 'uploads/output.wav';
    await this.audioService.convertPcmToWavBinary8Khz(inputFile,outputFile);

    const wavBuffer = fs.readFileSync(outputFile);
    onDone(wavBuffer.toString('base64'));

    // // const wavBuffer = this.audioService.createWavFileFromPCM(buffer, 24000);
    // fs.writeFile('uploads/model-response.wav', wavBuffer, () => {
    //   console.log('WAV dosyası oluşturuldu: model-response.wav');
    // });


    // const mono = fs.readFileSync('uploads/mono.wav');
    // const mono = fs.readFileSync('uploads/model-response-8khz.wav');
    // const mono = fs.readFileSync('uploads/model-response-direct.wav');
    // const mono = fs.readFileSync('uploads/model-response.wav');
    // onDone(mono.toString('base64'));
  }
}
